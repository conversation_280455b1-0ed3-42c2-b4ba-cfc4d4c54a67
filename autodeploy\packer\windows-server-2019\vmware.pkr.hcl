# Windows Server 2019 VMware Template
# Based on existing VMware deployment patterns from VMwareDeploy-latest.ps1

packer {
  required_plugins {
    vsphere = {
      version = ">= 1.2.0"
      source  = "github.com/hashicorp/vsphere"
    }
  }
}

# Variables
variable "vcenter_server" {
  type        = string
  description = "vCenter server FQDN or IP"
}

variable "vcenter_username" {
  type        = string
  description = "vCenter username"
}

variable "vcenter_password" {
  type        = string
  description = "vCenter password"
  sensitive   = true
}

variable "datacenter" {
  type        = string
  description = "vSphere datacenter"
}

variable "cluster" {
  type        = string
  description = "vSphere cluster"
}

variable "host" {
  type        = string
  description = "ESXi host"
  default     = ""
}

variable "datastore" {
  type        = string
  description = "vSphere datastore"
}

variable "network" {
  type        = string
  description = "vSphere network/port group"
}

variable "folder" {
  type        = string
  description = "VM folder path"
  default     = ""
}

variable "iso_path" {
  type        = string
  description = "Path to Windows Server 2019 ISO"
}

variable "vm_name" {
  type        = string
  description = "VM template name"
  default     = "windows-server-2019-template"
}

variable "vm_cpu_num" {
  type        = number
  description = "Number of CPUs"
  default     = 2
}

variable "vm_mem_size" {
  type        = number
  description = "Memory size in MB"
  default     = 4096
}

variable "vm_disk_size" {
  type        = number
  description = "Disk size in MB"
  default     = 61440  # 60GB
}

variable "winrm_username" {
  type        = string
  description = "WinRM username"
  default     = "Administrator"
}

variable "winrm_password" {
  type        = string
  description = "WinRM password"
  sensitive   = true
}

# Source configuration
source "vsphere-iso" "windows-server-2019" {
  # vCenter connection
  vcenter_server      = var.vcenter_server
  username            = var.vcenter_username
  password            = var.vcenter_password
  insecure_connection = true

  # VM location
  datacenter = var.datacenter
  cluster    = var.cluster
  host       = var.host
  datastore  = var.datastore
  folder     = var.folder

  # VM configuration
  vm_name              = var.vm_name
  guest_os_type        = "windows9Server64Guest"
  firmware             = "efi"
  CPUs                 = var.vm_cpu_num
  RAM                  = var.vm_mem_size
  RAM_reserve_all      = false
  disk_controller_type = ["lsilogic-sas"]
  
  storage {
    disk_size             = var.vm_disk_size
    disk_thin_provisioned = true
  }

  network_adapters {
    network      = var.network
    network_card = "vmxnet3"
  }

  # ISO configuration
  iso_paths = [var.iso_path]
  
  # Floppy for unattended installation
  floppy_files = [
    "../answer-files/windows-server-2019/autounattend.xml",
    "../scripts/setup/enable-winrm.ps1"
  ]

  # Boot configuration
  boot_wait    = "3s"
  boot_command = ["<enter>"]

  # WinRM configuration
  communicator   = "winrm"
  winrm_username = var.winrm_username
  winrm_password = var.winrm_password
  winrm_timeout  = "30m"

  # Shutdown configuration
  shutdown_command = "shutdown /s /t 10 /f /d p:4:1 /c \"Packer Shutdown\""
  shutdown_timeout = "15m"

  # Template conversion
  convert_to_template = true
}

# Build configuration
build {
  sources = ["source.vsphere-iso.windows-server-2019"]

  # Wait for WinRM to be available
  provisioner "powershell" {
    inline = ["Write-Host 'WinRM is ready'"]
  }

  # Install Windows Updates
  provisioner "powershell" {
    script = "../scripts/windows-updates.ps1"
  }

  # Install VMware Tools
  provisioner "powershell" {
    script = "../scripts/install-vmware-tools.ps1"
  }

  # Configure Windows features and roles
  provisioner "powershell" {
    script = "../scripts/configure-windows-features.ps1"
  }

  # Install common software
  provisioner "powershell" {
    script = "../scripts/install-common-software.ps1"
  }

  # Security hardening
  provisioner "powershell" {
    script = "../scripts/security-hardening.ps1"
  }

  # Final cleanup
  provisioner "powershell" {
    script = "../scripts/cleanup.ps1"
  }

  # Sysprep
  provisioner "powershell" {
    script = "../scripts/sysprep.ps1"
  }
}
