#Requires -RunAsAdministrator

# Build Hyper-V Local Test Script for Windows Server 2019
# This script builds a Windows Server 2019 VM locally using Hyper-V for testing

param(
    [Parameter(Mandatory=$false)]
    [string]$VarFile = "../../variables/hyperv-2019.pkrvars.hcl",

    [Parameter(Mandatory=$false)]
    [switch]$DebugMode,

    [Parameter(Mandatory=$false)]
    [switch]$SkipUpdates,

    [Parameter(Mandatory=$false)]
    [switch]$ValidateOnly
)

Write-Host "Building Hyper-V Windows Server 2019 Local Test VM" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator for Hyper-V access" -ForegroundColor Red
    Write-Host "Please run PowerShell as Administrator and try again" -ForegroundColor Yellow
    exit 1
}

# Check if Hyper-V is enabled
try {
    $hypervFeature = Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-All
    if ($hypervFeature.State -ne "Enabled") {
        Write-Host "ERROR: Hyper-V is not enabled on this system" -ForegroundColor Red
        Write-Host "Please enable Hyper-V and restart your computer:" -ForegroundColor Yellow
        Write-Host "Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All" -ForegroundColor Cyan
        exit 1
    }
} catch {
    Write-Host "WARNING: Could not verify Hyper-V status: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Check if Packer is installed
try {
    $packerVersion = packer version
    Write-Host "Packer version: $packerVersion" -ForegroundColor Cyan
} catch {
    Write-Host "ERROR: Packer is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Packer from: https://www.packer.io/downloads" -ForegroundColor Yellow
    exit 1
}

# Enable debug logging if requested
if ($DebugMode) {
    $env:PACKER_LOG = "1"
    Write-Host "Debug logging enabled" -ForegroundColor Yellow
}

# Check if ISO file exists
$varFileContent = Get-Content $VarFile -Raw
if ($varFileContent -match 'iso_path\s*=\s*"([^"]+)"') {
    $isoPath = $matches[1]
    
    # Adjust ISO path relative to script location (scripts/build/)
    if ($isoPath.StartsWith("./")) {
        $isoPath = "../../" + $isoPath.Substring(2)
    } elseif ($isoPath.StartsWith(".\\")) {
        $isoPath = "..\..\\" + $isoPath.Substring(3)
    }
    
    if (-not (Test-Path $isoPath)) {
        Write-Host "ERROR: ISO file not found at: $isoPath" -ForegroundColor Red
        Write-Host "Please ensure the Windows Server 2019 ISO is available at the specified path" -ForegroundColor Yellow
        Write-Host "Expected path from script location: $isoPath" -ForegroundColor Cyan
        exit 1
    }
    Write-Host "ISO file found: $isoPath" -ForegroundColor Green
}

# Check if required files exist
$requiredFiles = @(
    "../../windows-server-2019/hyperv.pkr.hcl",
    "../../answer-files/windows-server-2019/autounattend.xml",
    "../../scripts/configure-windows-features.ps1",
    "../../scripts/install-common-software.ps1",
    "../../scripts/security-hardening.ps1",
    "../../scripts/cleanup.ps1"
)

foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        Write-Host "ERROR: Required file not found: $file" -ForegroundColor Red
        exit 1
    }
}

Write-Host "All required files found" -ForegroundColor Green

# Validate the template
Write-Host "Validating Hyper-V template..." -ForegroundColor Cyan
try {
    packer validate -var-file="$VarFile" "../../windows-server-2019/hyperv.pkr.hcl"
    
    if ($LASTEXITCODE -ne 0) {
        throw "Template validation failed"
    }
    
    Write-Host "Template validation successful" -ForegroundColor Green
    
    if ($ValidateOnly) {
        Write-Host "Validation complete - template is ready for building" -ForegroundColor Green
        exit 0
    }
    
} catch {
    Write-Host "ERROR: Template validation failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Build the template
Write-Host "Starting Hyper-V build..." -ForegroundColor Cyan
Write-Host "This will create a new Hyper-V VM and run all provisioning scripts" -ForegroundColor Yellow

if ($SkipUpdates) {
    Write-Host "WARNING: Skipping Windows Updates for faster testing" -ForegroundColor Yellow
}

$startTime = Get-Date

try {
    # Build the template
    packer build -var-file="$VarFile" "../../windows-server-2019/hyperv.pkr.hcl"
    
    if ($LASTEXITCODE -ne 0) {
        throw "Template build failed"
    }
    
    $endTime = Get-Date
    $duration = $endTime - $startTime
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "LOCAL HYPER-V BUILD COMPLETED!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "Build duration: $($duration.ToString('hh\:mm\:ss'))" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Your scripts have been validated and are ready for:" -ForegroundColor White
    Write-Host "✅ VMware vSphere deployment" -ForegroundColor Green
    Write-Host "✅ AWS AMI creation" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Test the created VM in Hyper-V Manager" -ForegroundColor White
    Write-Host "2. Verify all software and configurations" -ForegroundColor White
    Write-Host "3. Run your VMware/AWS builds with confidence" -ForegroundColor White
    
} catch {
    Write-Host "ERROR: Failed to build Hyper-V template: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting tips:" -ForegroundColor Yellow
    Write-Host "1. Check Hyper-V Manager for any VMs that need cleanup" -ForegroundColor White
    Write-Host "2. Ensure sufficient disk space (60GB+ required)" -ForegroundColor White
    Write-Host "3. Verify network connectivity for Windows Updates" -ForegroundColor White
    Write-Host "4. Run with -DebugMode flag for detailed logging" -ForegroundColor White
    exit 1
}

# Cleanup environment variables
if ($DebugMode) {
    Remove-Item Env:PACKER_LOG -ErrorAction SilentlyContinue
}
