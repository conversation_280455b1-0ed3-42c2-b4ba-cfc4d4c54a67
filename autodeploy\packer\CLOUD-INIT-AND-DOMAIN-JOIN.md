# Cloud-Init and Domain Join in AWS

This document explains how cloud-init handles setup in AWS and compares it to traditional methods, plus covers domain joining strategies for cloud deployments.

## How Cloud-Init Works in AWS

### Boot Process Flow
```
EC2 Instance Starts → Cloud-Init Runs → User Data Executed → Packer Connects
```

### User Data Mechanism

When you launch an EC2 instance, you can provide **User Data** - a script that runs during first boot. In your Packer template, this is handled by the `user_data_file` parameter pointing to `scripts/setup/aws-userdata.ps1`.

### Cloud-Init vs Traditional Methods

| Method | How Files Are Delivered | When It Runs |
|--------|------------------------|--------------|
| **Hyper-V** | CD/DVD ISO attached to VM | During Windows Setup (autounattend.xml) |
| **VMware** | Floppy disk attached to VM | During Windows Setup (autounattend.xml) |
| **AWS** | User Data via metadata service | After Windows boots (cloud-init) |

### AWS User Data Process

#### Step 1: Instance Metadata Service
```
EC2 Instance → Queries → http://***************/latest/user-data
                      ↓
                Returns your PowerShell script
```

#### Step 2: EC2Config/EC2Launch Execution
- **Windows Server 2016+**: EC2Launch service runs user data
- **Windows Server 2012**: EC2Config service runs user data
- Scripts run with **SYSTEM privileges**

#### Step 3: Packer Connection
The user data script enables WinRM, creates HTTPS listener with self-signed cert, opens firewall port 5986, then Packer can connect via WinRM over HTTPS.

### Key Advantages of Cloud-Init Approach

#### No Physical Media Needed
- No floppy disks or CD/DVD ISOs to create
- User data is delivered via AWS metadata service
- Works with any AMI that has EC2Config/EC2Launch

#### Dynamic Configuration
```powershell
# User data can query AWS metadata for dynamic config
$instanceId = Invoke-RestMethod -Uri "http://***************/latest/meta-data/instance-id"
$region = Invoke-RestMethod -Uri "http://***************/latest/meta-data/placement/region"
```

#### Cloud-Native Integration
- Integrates with AWS Systems Manager
- Can download files from S3
- Can join Active Directory domains
- Can configure CloudWatch logging

### Comparison: Setup Methods

#### Traditional (Hyper-V/VMware)
```
Windows ISO → autounattend.xml → Automated Installation → WinRM Setup
     ↓              ↓                      ↓                    ↓
  Boot from     Answer file         Unattended setup      Packer connects
   media       on floppy/CD        during installation
```

#### AWS Cloud-Init
```
AMI Boot → Windows Starts → EC2Launch → User Data Script → WinRM Setup
    ↓           ↓              ↓             ↓                ↓
 Pre-built   Normal boot   Cloud service   Your script    Packer connects
   image                   runs script     enables WinRM
```

## Platform Requirements for ISO Creation Tools

### Linux Requirements by Builder

| Builder | ISO Creation Tool Needed? | Why? |
|---------|---------------------------|------|
| **Hyper-V** | ✅ **YES** (for `cd_files`) | Generation 2 VMs need CD/DVD for unattended files |
| **VMware** | ❌ **NO** | Uses `floppy_files` - no ISO creation needed |
| **AWS** | ❌ **NO** | Uses `user_data_file` - cloud-init handles setup |

### Linux ISO Creation Tools

Linux typically has these tools **pre-installed** or easily available:

```bash
# Most Linux distros have one of these by default:
which xorriso     # Usually available
which mkisofs     # Common on older systems  
which genisoimage # Alternative to mkisofs

# Install if needed:
# Ubuntu/Debian
sudo apt install xorriso

# RHEL/CentOS/Fedora  
sudo yum install xorriso
# or
sudo dnf install xorriso
```

### Key Differences

1. **Hyper-V** = Only platform that needs ISO creation tools (for `cd_files`)
2. **VMware** = Uses floppy drives (no ISO creation needed)
3. **AWS** = Uses cloud-init/user-data (no local files needed)
4. **Linux** = Usually has `xorriso`/`mkisofs` pre-installed

**Bottom line:** If you run VMware or AWS builders from Linux, you won't need to install additional ISO creation tools. Only Hyper-V requires them, and Linux typically has them already available.

## Domain Joining in AWS

### Current On-Premises Process (Traditional)
```
1. Pre-create computer object in specific OU
2. Use service account with delegated permissions
3. Join domain during deployment
4. Computer lands in predetermined OU
```

### AWS Cloud-Init Domain Join Methods

#### Method 1: AWS Systems Manager (Recommended)

```powershell
<powershell>
# In your user data script
# Download and execute AWS domain join SSM document
$document = "AWS-JoinDirectoryServiceDomain"
$parameters = @{
    "directoryId" = "d-**********"
    "directoryName" = "corp.company.com"
    "dnsIpAddresses" = @("**********", "**********")
}

# This uses AWS Directory Service integration
Start-SSMAssociation -Name $document -Parameters $parameters
</powershell>
```

#### Method 2: Traditional Domain Join via User Data

```powershell
<powershell>
# Store domain credentials in AWS Secrets Manager or Parameter Store
$domainUser = (Get-SSMParameter -Name "/company/domain/join-user" -WithDecryption $true).Value
$domainPass = (Get-SSMParameter -Name "/company/domain/join-password" -WithDecryption $true).Value | ConvertTo-SecureString -AsPlainText -Force
$credential = New-Object System.Management.Automation.PSCredential($domainUser, $domainPass)

# Get instance metadata for computer name
$instanceId = Invoke-RestMethod -Uri "http://***************/latest/meta-data/instance-id"
$computerName = "AWS-$instanceId"

# Join domain
Add-Computer -DomainName "corp.company.com" -Credential $credential -NewName $computerName -Restart
</powershell>
```

#### Method 3: AWS Directory Service Seamless Join

For **AWS Managed Microsoft AD** or **AD Connector**:

```powershell
<powershell>
# Instance automatically joins domain if:
# 1. Instance is in VPC with Directory Service
# 2. Instance has proper IAM role
# 3. Security groups allow domain traffic

# The join happens automatically - no credentials needed!
# AWS handles the domain join using the directory service
</powershell>
```

## OU Placement Strategies in AWS

### Option 1: Pre-Create Computer Objects (Like Current Process)

```powershell
<powershell>
# Use service account to pre-create computer object
$instanceId = Invoke-RestMethod -Uri "http://***************/latest/meta-data/instance-id"
$region = Invoke-RestMethod -Uri "http://***************/latest/meta-data/placement/region"
$computerName = "AWS-$region-$instanceId"

# Pre-create in specific OU
$targetOU = "OU=AWS-Servers,OU=Cloud,DC=corp,DC=company,DC=com"

# Service account creates computer object first
New-ADComputer -Name $computerName -Path $targetOU -Enabled $true

# Then join domain
Add-Computer -DomainName "corp.company.com" -Credential $credential -NewName $computerName
</powershell>
```

### Option 2: Post-Join OU Movement

```powershell
<powershell>
# Join domain first (lands in default Computers container)
Add-Computer -DomainName "corp.company.com" -Credential $credential

# Then move to correct OU
$computerDN = "CN=$env:COMPUTERNAME,CN=Computers,DC=corp,DC=company,DC=com"
$targetOU = "OU=AWS-Servers,OU=Cloud,DC=corp,DC=company,DC=com"
Move-ADObject -Identity $computerDN -TargetPath $targetOU
</powershell>
```

### Option 3: Dynamic OU Based on AWS Tags

```powershell
<powershell>
# Get instance tags to determine OU placement
$instanceId = Invoke-RestMethod -Uri "http://***************/latest/meta-data/instance-id"
$region = Invoke-RestMethod -Uri "http://***************/latest/meta-data/placement/region"

# Query AWS API for instance tags
$tags = (Get-EC2Tag -Filter @{Name="resource-id";Values=$instanceId}).Tags
$environment = ($tags | Where-Object {$_.Key -eq "Environment"}).Value
$application = ($tags | Where-Object {$_.Key -eq "Application"}).Value

# Determine OU based on tags
$targetOU = "OU=$application,OU=$environment,OU=AWS,DC=corp,DC=company,DC=com"

# Create computer object in correct OU
New-ADComputer -Name $env:COMPUTERNAME -Path $targetOU
Add-Computer -DomainName "corp.company.com" -Credential $credential
</powershell>
```

## Security Considerations

### Credential Management

| Method | Pros | Cons |
|--------|------|------|
| **AWS Secrets Manager** | Encrypted, rotatable, audited | Additional AWS cost |
| **Systems Manager Parameter Store** | Free tier, encrypted | Less features than Secrets Manager |
| **IAM Roles + Directory Service** | No stored credentials | Requires AWS Managed AD |
| **Instance Metadata** | Simple | Credentials visible in user data |

### Service Account Permissions

```powershell
# Minimal permissions needed for domain join service account:
# 1. Create computer objects in target OUs
# 2. Reset computer account passwords
# 3. Validated write to DNS host name
# 4. Validated write to service principal name

# Example delegation:
Grant-ADPermission -Identity "CN=AWS-DomainJoin-SvcAcct,OU=Service Accounts,DC=corp,DC=company,DC=com" `
                   -TargetOU "OU=AWS-Servers,OU=Cloud,DC=corp,DC=company,DC=com" `
                   -Rights "CreateChild,DeleteChild,WriteProperty"
```

## Integration with Your Packer Templates

You could add domain join capability to your AWS template by creating a new script like `scripts/domain-join-aws.ps1` that handles the cloud-specific domain join logic, keeping it separate from your on-premises domain join processes.

## Key Advantages of Cloud-Init Domain Join

1. **Dynamic Configuration**: Can use instance metadata, tags, and AWS APIs
2. **Credential Security**: Integration with AWS Secrets Manager/Parameter Store
3. **Automation**: No manual computer object pre-creation required
4. **Scalability**: Works with Auto Scaling Groups
5. **Auditability**: All actions logged in CloudTrail

The main difference is that cloud-init gives you **runtime flexibility** - you can make domain join decisions based on the actual deployed environment rather than having to pre-configure everything.

## Summary

The beauty of your Packer setup is that it abstracts these differences - the same provisioning scripts run regardless of whether the initial setup used autounattend.xml (local/VMware) or cloud-init (AWS). Cloud-init provides powerful runtime capabilities that traditional methods cannot match, especially for dynamic configuration and cloud-native integrations.
